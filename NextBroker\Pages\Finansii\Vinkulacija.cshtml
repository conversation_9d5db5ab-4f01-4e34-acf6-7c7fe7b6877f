@page
@model NextBroker.Pages.Finansii.VinkulacijaModel
@{
    ViewData["Title"] = "Винкулација";
    Layout = "_Layout";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-file-contract mr-2"></i>
                        Винкулација
                    </h3>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            @TempData["ErrorMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    <!-- Form Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <button class="btn btn-link p-0 text-left" type="button" data-toggle="collapse" data-target="#formSection" aria-expanded="false" aria-controls="formSection">
                                    <i class="fas fa-plus-circle mr-2"></i>
                                    Додај нова винкулација
                                </button>
                            </h4>
                        </div>
                        <div class="collapse" id="formSection">
                            <div class="card-body">
                                <form method="post">
                                    @Html.AntiForgeryToken()
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.BrojNaVinkulacija" class="form-label"></label>
                                                <input asp-for="Input.BrojNaVinkulacija" class="form-control" />
                                                <span asp-validation-for="Input.BrojNaVinkulacija" class="text-danger"></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.BrojNaPolisa" class="form-label"></label>
                                                <div class="input-group">
                                                    <input asp-for="Input.BrojNaPolisa" class="form-control" id="brojNaPolisaInput" autocomplete="off" />
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary" type="button" id="searchPolisiBtn">
                                                            <i class="fas fa-search"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div id="polisiResults" class="search-results"></div>
                                                <span asp-validation-for="Input.BrojNaPolisa" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.Osiguritel" class="form-label"></label>
                                                <select asp-for="Input.Osiguritel" asp-items="Model.Osiguriteli" class="form-control">
                                                    <option value="">-- Избери осигурител --</option>
                                                </select>
                                                <span asp-validation-for="Input.Osiguritel" class="text-danger"></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.Klasa" class="form-label"></label>
                                                <select asp-for="Input.Klasa" asp-items="Model.Klasi" class="form-control">
                                                    <option value="">-- Избери класа --</option>
                                                </select>
                                                <span asp-validation-for="Input.Klasa" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.Proizvod" class="form-label"></label>
                                                <select asp-for="Input.Proizvod" asp-items="Model.Produkti" class="form-control">
                                                    <option value="">-- Избери производ --</option>
                                                </select>
                                                <span asp-validation-for="Input.Proizvod" class="text-danger"></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.VinkuliranaVoKoristNa" class="form-label"></label>
                                                <div class="input-group">
                                                    <input asp-for="Input.VinkuliranaVoKoristNa" class="form-control" id="klientInput" autocomplete="off" />
                                                    <div class="input-group-append">
                                                        <button class="btn btn-outline-secondary" type="button" id="searchKlientBtn">
                                                            <i class="fas fa-search"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div id="klientResults" class="search-results"></div>
                                                <span asp-validation-for="Input.VinkuliranaVoKoristNa" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.VinkulacijaOdDatum" class="form-label"></label>
                                                <input asp-for="Input.VinkulacijaOdDatum" type="date" class="form-control" />
                                                <span asp-validation-for="Input.VinkulacijaOdDatum" class="text-danger"></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label asp-for="Input.VinkulacijaDoDatum" class="form-label"></label>
                                                <input asp-for="Input.VinkulacijaDoDatum" type="date" class="form-control" />
                                                <span asp-validation-for="Input.VinkulacijaDoDatum" class="text-danger"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save mr-2"></i>
                                            Зачувај
                                        </button>
                                        <button type="reset" class="btn btn-secondary ml-2">
                                            <i class="fas fa-undo mr-2"></i>
                                            Ресетирај
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Table Section -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title mb-0">
                                <i class="fas fa-table mr-2"></i>
                                Преглед на винкулации
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="vinkulacijaTable" class="table table-striped table-bordered table-hover">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>ID</th>
                                            <th>Датум</th>
                                            <th>Корисник</th>
                                            <th>Број винкулација</th>
                                            <th>Број полиса</th>
                                            <th>Осигурител</th>
                                            <th>Класа</th>
                                            <th>Производ</th>
                                            <th>Од датум</th>
                                            <th>До датум</th>
                                            <th>Винкулирана во корист на</th>
                                            <th>Акции</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var entry in Model.Entries)
                                        {
                                            <tr>
                                                <td>@entry.Id</td>
                                                <td>@entry.DateCreated?.ToString("dd.MM.yyyy HH:mm")</td>
                                                <td>@entry.UsernameCreated</td>
                                                <td>@entry.BrojNaVinkulacija</td>
                                                <td>@entry.BrojNaPolisa</td>
                                                <td>@entry.Osiguritel</td>
                                                <td>@entry.Klasa</td>
                                                <td>@entry.Proizvod</td>
                                                <td>@entry.VinkulacijaOdDatum?.ToString("dd.MM.yyyy")</td>
                                                <td>@entry.VinkulacijaDoDatum?.ToString("dd.MM.yyyy")</td>
                                                <td>@entry.VinkuliranaVoKoristNa</td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger generate-pdf-btn" data-entry-id="@entry.Id" title="Генерирај PDF документ">
                                                        <i class="fas fa-file-pdf"></i> Генерирај документ
                                                    </button>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden div for PDF generation -->
<div id="pdfPreview" style="display: none; position: absolute; left: -9999px; top: -9999px;">
    <!-- PDF content will be inserted here -->
</div>

<!-- CSS Styles -->
<style>
    .search-results {
        position: absolute;
        z-index: 1000;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        max-height: 200px;
        overflow-y: auto;
        width: 100%;
        display: none;
    }

    .search-result-item {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
    }

    .search-result-item:hover {
        background-color: #f8f9fa;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }

    .input-group {
        position: relative;
    }

    .form-group {
        position: relative;
    }
</style>

@section Scripts {
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script>
        $(document).ready(function () {
            // Initialize DataTable with Macedonian localization
            $('#vinkulacijaTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.13.4/i18n/mk.json"
                },
                "pageLength": 25,
                "order": [[0, "desc"]],
                "columnDefs": [
                    { "orderable": false, "targets": [11] } // Disable sorting for Actions column
                ]
            });

            // Client search functionality
            let klientSearchTimeout;
            $('#klientInput').on('input', function () {
                clearTimeout(klientSearchTimeout);
                const searchTerm = $(this).val();

                if (searchTerm.length < 2) {
                    $('#klientResults').hide().empty();
                    return;
                }

                klientSearchTimeout = setTimeout(function () {
                    $.ajax({
                        url: '/Finansii/Vinkulacija?handler=SearchKlienti',
                        type: 'GET',
                        data: { mb: searchTerm },
                        headers: {
                            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function (data) {
                            $('#klientResults').empty();
                            if (data && data.length > 0) {
                                data.forEach(function (item) {
                                    const displayText = item.tip === 'P' ? item.naziv : `${item.ime} ${item.prezime}`;
                                    const searchInfo = `${item.mb || ''} ${item.edb || ''} ${item.embg || ''}`.trim();
                                    $('#klientResults').append(
                                        `<div class="search-result-item" data-naziv="${displayText}">
                                            <strong>${displayText}</strong><br>
                                            <small class="text-muted">${searchInfo}</small>
                                        </div>`
                                    );
                                });
                                $('#klientResults').show();
                            } else {
                                $('#klientResults').hide();
                            }
                        },
                        error: function () {
                            $('#klientResults').hide();
                        }
                    });
                }, 300);
            });

            // Handle client selection
            $(document).on('click', '.search-result-item', function () {
                const naziv = $(this).data('naziv');
                $('#klientInput').val(naziv);
                $('#klientResults').hide();
            });

            // Search button for clients
            $('#searchKlientBtn').click(function () {
                $('#klientInput').trigger('input');
            });

            // Hide search results when clicking outside
            $(document).click(function (e) {
                if (!$(e.target).closest('#klientInput, #klientResults').length) {
                    $('#klientResults').hide();
                }
                if (!$(e.target).closest('#brojNaPolisaInput, #polisiResults').length) {
                    $('#polisiResults').hide();
                }
            });

            // Polisi search functionality
            let polisiSearchTimeout;
            $('#brojNaPolisaInput').on('input', function () {
                clearTimeout(polisiSearchTimeout);
                const searchTerm = $(this).val();

                if (searchTerm.length < 2) {
                    $('#polisiResults').hide().empty();
                    return;
                }

                polisiSearchTimeout = setTimeout(function () {
                    $.ajax({
                        url: '/Finansii/Vinkulacija?handler=SearchPolisi',
                        type: 'GET',
                        data: { broj: searchTerm },
                        headers: {
                            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function (data) {
                            $('#polisiResults').empty();
                            if (data && data.length > 0) {
                                data.forEach(function (item) {
                                    $('#polisiResults').append(
                                        `<div class="search-result-item" data-broj="${item.brojNaPolisa}">
                                            <strong>${item.brojNaPolisa}</strong>
                                        </div>`
                                    );
                                });
                                $('#polisiResults').show();
                            } else {
                                $('#polisiResults').hide();
                            }
                        },
                        error: function () {
                            $('#polisiResults').hide();
                        }
                    });
                }, 300);
            });

            // Handle polisi selection
            $(document).on('click', '#polisiResults .search-result-item', function () {
                const broj = $(this).data('broj');
                $('#brojNaPolisaInput').val(broj);
                $('#polisiResults').hide();

                // Auto-populate fields based on selected polisa
                fetchPolisaDetails(broj);
            });

            // Search button for polisi
            $('#searchPolisiBtn').click(function () {
                $('#brojNaPolisaInput').trigger('input');
            });

            // Handle PDF generation button clicks
            $('.generate-pdf-btn').on('click', function() {
                const entryId = $(this).data('entry-id');
                const button = $(this);
                generateVinkulacijaPDF(entryId, button);
            });
        });

        // Function to fetch polisa details and auto-populate fields
        function fetchPolisaDetails(brojNaPolisa) {
            if (!brojNaPolisa) return;

            $.ajax({
                url: '/Finansii/Vinkulacija?handler=PolisaDetails',
                type: 'GET',
                data: { brojNaPolisa: brojNaPolisa },
                headers: {
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                success: function (response) {
                    if (response.success) {
                        // Auto-populate Osiguritel dropdown
                        if (response.osiguritel) {
                            $('#Input_Osiguritel').val(response.osiguritel);
                        }

                        // Auto-populate Klasa dropdown
                        if (response.klasa) {
                            $('#Input_Klasa').val(response.klasa);
                        }

                        // Auto-populate Proizvod dropdown
                        if (response.proizvod) {
                            $('#Input_Proizvod').val(response.proizvod);
                        }

                        // Optional: Show success message
                        console.log('Полисата е успешно пронајдена и податоците се пополнети автоматски.');
                    } else {
                        console.warn('Грешка при пронаоѓање на полисата: ' + response.error);
                    }
                },
                error: function () {
                    console.error('Грешка при повикување на серверот за детали за полисата.');
                }
            });
        }

        function generateVinkulacijaPDF(entryId, button) {
            // Show loading state
            const originalText = button.html();
            button.html('<i class="fas fa-spinner fa-spin"></i> Генерирање...');
            button.prop('disabled', true);

            // Get the document HTML from server
            $.get('@Url.Page("/Finansii/Vinkulacija", "GenerateDocument")', { id: entryId })
                .done(function(response) {
                    if (response.success) {
                        createPDFFromHtml(response.html, entryId, button, originalText);
                    } else {
                        button.html(originalText);
                        button.prop('disabled', false);
                        alert('Грешка: ' + response.error);
                    }
                })
                .fail(function() {
                    button.html(originalText);
                    button.prop('disabled', false);
                    alert('Грешка при генерирање на документот.');
                });
        }

        function createPDFFromHtml(htmlContent, entryId, button, originalText) {
            // Insert HTML into hidden preview div
            const previewDiv = document.getElementById('pdfPreview');
            previewDiv.innerHTML = htmlContent;

            // Set up the preview div for proper PDF capture
            previewDiv.style.display = 'block';
            previewDiv.style.position = 'static';
            previewDiv.style.left = 'auto';
            previewDiv.style.top = 'auto';
            previewDiv.style.width = '794px'; // A4 width in pixels at 96 DPI
            previewDiv.style.padding = '20px';
            previewDiv.style.backgroundColor = '#ffffff';

            // Use html2canvas to capture the element
            html2canvas(previewDiv, {
                scale: 2, // Higher scale for better quality
                useCORS: true,
                logging: false,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: 794 + 40, // Include padding
                height: null // Let it calculate height automatically
            }).then(canvas => {
                // Hide the preview div again
                previewDiv.style.display = 'none';
                previewDiv.style.position = 'absolute';
                previewDiv.style.left = '-9999px';
                previewDiv.style.top = '-9999px';
                previewDiv.style.width = 'auto';
                previewDiv.style.padding = '0';

                // Initialize jsPDF
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF('p', 'mm', 'a4', true);

                // Calculate dimensions to fill the page
                const imgData = canvas.toDataURL('image/jpeg', 0.9);
                const pageWidth = doc.internal.pageSize.getWidth();
                const pageHeight = doc.internal.pageSize.getHeight();

                // Calculate the aspect ratio of the captured image
                const imgWidth = canvas.width;
                const imgHeight = canvas.height;
                const imgAspectRatio = imgWidth / imgHeight;

                // Calculate dimensions to fit the page while maintaining aspect ratio
                let pdfWidth = pageWidth;
                let pdfHeight = pageWidth / imgAspectRatio;

                // If the calculated height exceeds page height, scale down
                if (pdfHeight > pageHeight) {
                    pdfHeight = pageHeight;
                    pdfWidth = pageHeight * imgAspectRatio;
                }

                // Center the image on the page
                const x = (pageWidth - pdfWidth) / 2;
                const y = (pageHeight - pdfHeight) / 2;

                // Add image to PDF with calculated dimensions
                doc.addImage(imgData, 'JPEG', x, y, pdfWidth, pdfHeight);

                // Generate filename and save
                const fileName = `Vinkulacija_${entryId}_${new Date().toISOString().slice(0, 10)}.pdf`;
                doc.save(fileName);

                // Restore button state
                button.html(originalText);
                button.prop('disabled', false);
            }).catch(error => {
                console.error('Error generating PDF:', error);
                button.html(originalText);
                button.prop('disabled', false);
                alert('Грешка при генерирање на PDF документот.');
            });
        }
    </script>
}
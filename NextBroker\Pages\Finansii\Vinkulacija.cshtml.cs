using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;

namespace NextBroker.Pages.Finansii
{
    public class VinkulacijaModel : SecurePageModel
    {
        public VinkulacijaModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("Vinkulacija"))
            {
                return RedirectToAccessDenied();
            }

            return Page();
        }
    }
}
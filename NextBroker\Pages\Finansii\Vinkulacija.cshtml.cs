using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using RazorPortal.Services;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace NextBroker.Pages.Finansii
{
    public class VinkulacijaModel : SecurePageModel
    {
        public VinkulacijaModel(IConfiguration configuration)
            : base(configuration)
        {
        }

        [BindProperty]
        public VinkulacijaInputModel Input { get; set; } = new();

        public IEnumerable<SelectListItem> Osiguriteli { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> Klasi { get; set; } = new List<SelectListItem>();
        public IEnumerable<SelectListItem> Produkti { get; set; } = new List<SelectListItem>();
        public List<VinkulacijaEntry> Entries { get; set; } = new List<VinkulacijaEntry>();

        public async Task<IActionResult> OnGet()
        {
            if (!await HasPageAccess("Vinkulacija"))
            {
                return RedirectToAccessDenied();
            }

            await LoadDropdownData();
            await LoadEntries();
            return Page();
        }

        public async Task<IActionResult> OnGetGenerateDocument(long id)
        {
            if (!await HasPageAccess("Vinkulacija"))
            {
                return RedirectToAccessDenied();
            }

            try
            {
                // Get the specific entry by ID
                var entry = await GetVinkulacijaEntryById(id);
                if (entry == null)
                {
                    return new JsonResult(new { success = false, error = "Записот не е пронајден." });
                }

                // Generate HTML document
                var documentHtml = GenerateVinkulacijaHtml(entry);

                // Return the HTML as JSON for AJAX consumption
                return new JsonResult(new { success = true, html = documentHtml });
            }
            catch (Exception ex)
            {
                return new JsonResult(new { success = false, error = ex.Message });
            }
        }

        public async Task<IActionResult> OnPost()
        {
            if (!await HasPageAccess("Vinkulacija"))
            {
                return RedirectToAccessDenied();
            }

            if (!ModelState.IsValid)
            {
                await LoadDropdownData();
                await LoadEntries();
                return Page();
            }

            string username = HttpContext.Session.GetString("Username");
            if (string.IsNullOrEmpty(username))
            {
                TempData["ErrorMessage"] = "Вашата сесија е истечена. Најавете се повторно.";
                return RedirectToPage("/Account/Login");
            }

            try
            {
                await InsertVinkulacija(username);
                TempData["SuccessMessage"] = "Винкулацијата е успешно додадена.";
                return RedirectToPage();
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Грешка при додавање: {ex.Message}";
                await LoadDropdownData();
                await LoadEntries();
                return Page();
            }
        }

        private async Task LoadDropdownData()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Load Osiguriteli
                using (SqlCommand cmd = new SqlCommand("SELECT Naziv FROM Klienti WHERE Osiguritel = 1", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var osiguriteli = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string naziv = reader["Naziv"].ToString();
                        osiguriteli.Add(new SelectListItem(naziv, naziv));
                    }
                    Osiguriteli = osiguriteli;
                }

                // Load Klasi
                using (SqlCommand cmd = new SqlCommand("SELECT KlasaIme FROM KlasiOsiguruvanje", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var klasi = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string klasaIme = reader["KlasaIme"].ToString();
                        klasi.Add(new SelectListItem(klasaIme, klasaIme));
                    }
                    Klasi = klasi;
                }

                // Load Produkti
                using (SqlCommand cmd = new SqlCommand("SELECT Ime FROM Produkti", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var produkti = new List<SelectListItem>();
                    while (await reader.ReadAsync())
                    {
                        string ime = reader["Ime"].ToString();
                        produkti.Add(new SelectListItem(ime, ime));
                    }
                    Produkti = produkti;
                }
            }
        }

        private async Task LoadEntries()
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, BrojNaVinkulacija,
                           BrojNaPolisa, Osiguritel, Klasa, Proizvod,
                           VinkulacijaOdDatum, VinkulacijaDoDatum, VinkuliranaVoKoristNa
                    FROM Vinkulacija
                    ORDER BY DateCreated DESC", connection))
                {
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var entries = new List<VinkulacijaEntry>();
                    while (await reader.ReadAsync())
                    {
                        entries.Add(new VinkulacijaEntry
                        {
                            Id = (long)reader["Id"],
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            BrojNaVinkulacija = reader["BrojNaVinkulacija"] as string,
                            BrojNaPolisa = reader["BrojNaPolisa"] as string,
                            Osiguritel = reader["Osiguritel"] as string,
                            Klasa = reader["Klasa"] as string,
                            Proizvod = reader["Proizvod"] as string,
                            VinkulacijaOdDatum = reader["VinkulacijaOdDatum"] as DateTime?,
                            VinkulacijaDoDatum = reader["VinkulacijaDoDatum"] as DateTime?,
                            VinkuliranaVoKoristNa = reader["VinkuliranaVoKoristNa"] as string
                        });
                    }
                    Entries = entries;
                }
            }
        }

        private async Task InsertVinkulacija(string username)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    INSERT INTO Vinkulacija (
                        UsernameCreated, BrojNaVinkulacija, BrojNaPolisa, Osiguritel, Klasa, Proizvod,
                        VinkulacijaOdDatum, VinkulacijaDoDatum, VinkuliranaVoKoristNa
                    ) VALUES (
                        @UsernameCreated, @BrojNaVinkulacija, @BrojNaPolisa, @Osiguritel, @Klasa, @Proizvod,
                        @VinkulacijaOdDatum, @VinkulacijaDoDatum, @VinkuliranaVoKoristNa
                    )", connection))
                {
                    cmd.Parameters.AddWithValue("@UsernameCreated", username);
                    cmd.Parameters.AddWithValue("@BrojNaVinkulacija", (object)Input.BrojNaVinkulacija ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@BrojNaPolisa", (object)Input.BrojNaPolisa ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Osiguritel", (object)Input.Osiguritel ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Klasa", (object)Input.Klasa ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@Proizvod", (object)Input.Proizvod ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@VinkulacijaOdDatum", (object)Input.VinkulacijaOdDatum ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@VinkulacijaDoDatum", (object)Input.VinkulacijaDoDatum ?? DBNull.Value);
                    cmd.Parameters.AddWithValue("@VinkuliranaVoKoristNa", (object)Input.VinkuliranaVoKoristNa ?? DBNull.Value);

                    await cmd.ExecuteNonQueryAsync();
                }
            }
        }

        private async Task<VinkulacijaEntry?> GetVinkulacijaEntryById(long id)
        {
            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT Id, DateCreated, UsernameCreated, BrojNaVinkulacija,
                           BrojNaPolisa, Osiguritel, Klasa, Proizvod,
                           VinkulacijaOdDatum, VinkulacijaDoDatum, VinkuliranaVoKoristNa
                    FROM Vinkulacija
                    WHERE Id = @Id", connection))
                {
                    cmd.Parameters.AddWithValue("@Id", id);
                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();

                    if (await reader.ReadAsync())
                    {
                        return new VinkulacijaEntry
                        {
                            Id = Convert.ToInt64(reader["Id"]),
                            DateCreated = reader["DateCreated"] as DateTime?,
                            UsernameCreated = reader["UsernameCreated"] as string,
                            BrojNaVinkulacija = reader["BrojNaVinkulacija"] as string,
                            BrojNaPolisa = reader["BrojNaPolisa"] as string,
                            Osiguritel = reader["Osiguritel"] as string,
                            Klasa = reader["Klasa"] as string,
                            Proizvod = reader["Proizvod"] as string,
                            VinkulacijaOdDatum = reader["VinkulacijaOdDatum"] as DateTime?,
                            VinkulacijaDoDatum = reader["VinkulacijaDoDatum"] as DateTime?,
                            VinkuliranaVoKoristNa = reader["VinkuliranaVoKoristNa"] as string
                        };
                    }
                }
            }
            return null;
        }

        private string GenerateVinkulacijaHtml(VinkulacijaEntry entry)
        {
            var html = $@"
                <div style=""font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; position: relative; padding: 20px 40px;"">
                    <!-- Background Logo -->
                    <div style=""position: absolute; top: 20px; right: 40px; width: 150px; opacity: 0.15; z-index: 0; pointer-events: none;"">
                        <img src=""/images/logo/INCO_LOGO_Regular.svg"" style=""width: 100%; height: auto;"" />
                    </div>

                    <!-- Decorative Corner Elements -->
                    <div style=""position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);""></div>
                    <div style=""position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-top: 1px solid rgba(212, 175, 55, 0.25);""></div>
                    <div style=""position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-left: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);""></div>
                    <div style=""position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-right: 1px solid rgba(212, 175, 55, 0.25); border-bottom: 1px solid rgba(212, 175, 55, 0.25);""></div>

                    <div style=""position: relative; z-index: 1;"">
                        <div style=""text-align: left; margin-bottom: 20px; padding-right: 160px;"">
                            <h3 style=""color: #2F4F4F; margin-bottom: 5px; font-size: 22px; text-shadow: 1px 1px 1px rgba(0,0,0,0.1);"">Осигурително Брокерско Друштво ИНКО АД Скопје</h3>
                            <h4 style=""color: #000; margin-top: 0; font-size: 18px;"">ВИНКУЛАЦИЈА БРОЈ: {entry.Id}</h4>
                        </div>

                        <div style=""margin-bottom: 20px; background-color: rgba(47, 79, 79, 0.03); padding: 15px; border-radius: 6px; border: 1px solid rgba(212, 175, 55, 0.2); box-shadow: 0 1px 3px rgba(0,0,0,0.05);"">
                            <p style=""margin: 5px 0; font-size: 13px;""><strong style=""color: #2F4F4F; min-width: 180px; display: inline-block;"">Датум на креирање:</strong> {entry.DateCreated?.ToString("dd.MM.yyyy")}</p>
                        </div>

                        <table style=""width: 100%; border-collapse: collapse; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); background-color: white;"">
                            <thead>
                                <tr style=""background: linear-gradient(90deg, #2F4F4F, #1a2f2f); color: white;"">
                                    <th style=""border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;"">Поле</th>
                                    <th style=""border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 13px;"">Вредност</th>
                                </tr>
                            </thead>
                            <tbody>";

            // Add data rows with alternating colors
            var fields = new[]
            {
                ("Број на винкулација", entry.BrojNaVinkulacija),
                ("Број на полиса", entry.BrojNaPolisa),
                ("Осигурител", entry.Osiguritel),
                ("Класа", entry.Klasa),
                ("Производ", entry.Proizvod),
                ("Винкулација од датум", entry.VinkulacijaOdDatum?.ToString("dd.MM.yyyy")),
                ("Винкулација до датум", entry.VinkulacijaDoDatum?.ToString("dd.MM.yyyy")),
                ("Винкулирана во корист на", entry.VinkuliranaVoKoristNa)
            };

            for (int i = 0; i < fields.Length; i++)
            {
                var backgroundColor = i % 2 == 0 ? "white" : "rgba(47, 79, 79, 0.02)";
                html += $@"
                                <tr style=""background-color: {backgroundColor};"">
                                    <td style=""border: 1px solid #ddd; padding: 8px; font-size: 13px; font-weight: bold; color: #2F4F4F;"">{fields[i].Item1}</td>
                                    <td style=""border: 1px solid #ddd; padding: 8px; font-size: 13px;"">{fields[i].Item2 ?? ""}</td>
                                </tr>";
            }

            html += @"
                            </tbody>
                        </table>

                        <div style=""margin-top: 30px; text-align: center; font-size: 12px; color: #666;"">
                            <p>Генерирано на: " + DateTime.Now.ToString("dd.MM.yyyy HH:mm") + @"</p>
                        </div>
                    </div>
                </div>";

            return html;
        }

        public async Task<IActionResult> OnGetSearchKlienti(string mb)
        {
            if (string.IsNullOrWhiteSpace(mb))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10
                        Id,
                        ISNULL(MB, '') as MB,
                        ISNULL(EDB, '') as EDB,
                        ISNULL(EMBG, '') as EMBG,
                        CASE
                            WHEN KlientFizickoPravnoLice = 'P' THEN Naziv
                            ELSE CONCAT(ISNULL(Ime, ''), ' ', ISNULL(Prezime, ''))
                        END as Naziv,
                        KlientFizickoPravnoLice,
                        ISNULL(Ime, '') as Ime,
                        ISNULL(Prezime, '') as Prezime
                    FROM Klienti
                    WHERE MB LIKE @Search + '%'
                       OR EDB LIKE @Search + '%'
                       OR EMBG LIKE @Search + '%'
                       OR Naziv LIKE '%' + @Search + '%'
                       OR Ime LIKE '%' + @Search + '%'
                       OR Prezime LIKE '%' + @Search + '%'
                    ORDER BY
                        CASE
                            WHEN MB LIKE @Search + '%' THEN 1
                            WHEN EDB LIKE @Search + '%' THEN 2
                            WHEN EMBG LIKE @Search + '%' THEN 3
                            ELSE 4
                        END,
                        MB, Naziv", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", mb);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            id = reader["Id"],
                            mb = reader["MB"],
                            edb = reader["EDB"],
                            embg = reader["EMBG"],
                            naziv = reader["Naziv"],
                            ime = reader["Ime"],
                            prezime = reader["Prezime"],
                            tip = reader["KlientFizickoPravnoLice"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }

        public async Task<IActionResult> OnGetSearchPolisi(string broj)
        {
            if (string.IsNullOrWhiteSpace(broj))
                return new JsonResult(new List<object>());

            string connectionString = _configuration.GetConnectionString("DefaultConnection");
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                using (SqlCommand cmd = new SqlCommand(@"
                    SELECT TOP 10 BrojNaPolisa
                    FROM Polisi
                    WHERE Storno != 1 AND BrojNaPolisa LIKE @Search + '%'
                    ORDER BY BrojNaPolisa", connection))
                {
                    cmd.Parameters.AddWithValue("@Search", broj);

                    using SqlDataReader reader = await cmd.ExecuteReaderAsync();
                    var results = new List<object>();
                    while (await reader.ReadAsync())
                    {
                        results.Add(new
                        {
                            brojNaPolisa = reader["BrojNaPolisa"].ToString()
                        });
                    }
                    return new JsonResult(results);
                }
            }
        }
    }

    public class VinkulacijaInputModel
    {
        [Display(Name = "Број на винкулација")]
        public string? BrojNaVinkulacija { get; set; }

        [Display(Name = "Број на полиса")]
        public string? BrojNaPolisa { get; set; }

        [Display(Name = "Осигурител")]
        public string? Osiguritel { get; set; }

        [Display(Name = "Класа")]
        public string? Klasa { get; set; }

        [Display(Name = "Производ")]
        public string? Proizvod { get; set; }

        [Display(Name = "Винкулација од датум")]
        public DateTime? VinkulacijaOdDatum { get; set; }

        [Display(Name = "Винкулација до датум")]
        public DateTime? VinkulacijaDoDatum { get; set; }

        [Display(Name = "Винкулирана во корист на")]
        public string? VinkuliranaVoKoristNa { get; set; }
    }

    public class VinkulacijaEntry
    {
        public long Id { get; set; }
        public DateTime? DateCreated { get; set; }
        public string? UsernameCreated { get; set; }
        public string? BrojNaVinkulacija { get; set; }
        public string? BrojNaPolisa { get; set; }
        public string? Osiguritel { get; set; }
        public string? Klasa { get; set; }
        public string? Proizvod { get; set; }
        public DateTime? VinkulacijaOdDatum { get; set; }
        public DateTime? VinkulacijaDoDatum { get; set; }
        public string? VinkuliranaVoKoristNa { get; set; }
    }
}